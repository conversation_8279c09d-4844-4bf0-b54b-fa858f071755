import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../home.dart';
import '../../services/app_initialization_manager.dart';
import '../../services/localization_service.dart';

/// 应用加载屏幕
/// 在应用启动时显示，管理初始化流程并提供视觉反馈
class AppLoadingScreen extends StatefulWidget {
  final LocalizationService localizationService;

  const AppLoadingScreen({super.key, required this.localizationService});

  @override
  State<AppLoadingScreen> createState() => _AppLoadingScreenState();
}

class _AppLoadingScreenState extends State<AppLoadingScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoAnimationController;
  late AnimationController _progressAnimationController;
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoOpacityAnimation;
  late Animation<double> _progressAnimation;

  String _currentStatus = '';



  /// 获取本地化的状态文本
  String _getLocalizedStatus(String statusKey) {
    final currentLang =
        widget.localizationService.currentLocale?.languageCode ??
        _getSystemLanguage();

    const statusTexts = {
      'initializing_core': {
        'zh': '正在初始化核心服务...',
        'en': 'Initializing core services...',
        'ja': 'コアサービスを初期化中...',
      },
      'preparing_ui': {
        'zh': '正在准备用户界面...',
        'en': 'Preparing user interface...',
        'ja': 'ユーザーインターフェースを準備中...',
      },
      'loading_features': {
        'zh': '正在加载应用功能...',
        'en': 'Loading application features...',
        'ja': 'アプリケーション機能を読み込み中...',
      },
      'startup_complete': {
        'zh': '启动完成',
        'en': 'Startup complete',
        'ja': '起動完了',
      },
      'initialization_failed': {
        'zh': '初始化失败，请重试',
        'en': 'Initialization failed, please retry',
        'ja': '初期化に失敗しました。再試行してください',
      },
    };

    final statusMap = statusTexts[statusKey];
    if (statusMap == null) return statusKey;

    return statusMap[currentLang] ?? statusMap['zh']!;
  }

  /// 获取系统语言
  String _getSystemLanguage() {
    final systemLocale = WidgetsBinding.instance.platformDispatcher.locale;
    return systemLocale.languageCode;
  }

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startInitialization();
  }

  @override
  void dispose() {
    _logoAnimationController.dispose();
    _progressAnimationController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _logoAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _logoScaleAnimation = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoAnimationController,
        curve: Curves.elasticOut,
      ),
    );

    _logoOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoAnimationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _progressAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _logoAnimationController.forward();
  }

  Future<void> _startInitialization() async {
    try {
      // 阶段1：初始化关键服务
      _updateStatus(_getLocalizedStatus('initializing_core'), 0.2);
      await AppInitializationManager.initializeCriticalServices();

      // 阶段2：准备用户界面
      _updateStatus(_getLocalizedStatus('preparing_ui'), 0.5);
      await Future.delayed(const Duration(milliseconds: 500)); // 让用户看到进度

      // 阶段3：在后台初始化非关键服务
      _updateStatus(_getLocalizedStatus('loading_features'), 0.8);

      // 启动非关键服务的后台初始化（不等待完成）
      AppInitializationManager.initializeNonCriticalServices();

      // 等待一小段时间确保UI准备就绪
      await Future.delayed(const Duration(milliseconds: 800));

      _updateStatus(_getLocalizedStatus('startup_complete'), 1.0);
      await Future.delayed(const Duration(milliseconds: 500));

      // 导航到主页面
      _navigateToHome();
    } catch (e) {
      debugPrint('初始化失败: $e');
      _updateStatus(_getLocalizedStatus('initialization_failed'), 0.0);

      // 显示错误对话框
      _showErrorDialog(e.toString());
    }
  }

  void _updateStatus(String status, double progress) {
    if (mounted) {
      setState(() {
        _currentStatus = status;
      });
      _progressAnimationController.animateTo(progress);
    }
  }

  void _navigateToHome() {
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder:
              (context, animation, secondaryAnimation) =>
                  HomePage(localizationService: widget.localizationService),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 500),
        ),
      );
    }
  }

  void _showErrorDialog(String error) {
    final currentLang =
        widget.localizationService.currentLocale?.languageCode ??
        _getSystemLanguage();

    const dialogTexts = {
      'title': {
        'zh': '初始化失败',
        'en': 'Initialization Failed',
        'ja': '初期化に失敗しました',
      },
      'content': {
        'zh': '应用启动时遇到问题：\n',
        'en': 'Problem occurred during app startup:\n',
        'ja': 'アプリ起動時に問題が発生しました：\n',
      },
      'retry': {'zh': '重试', 'en': 'Retry', 'ja': '再試行'},
      'exit': {'zh': '退出', 'en': 'Exit', 'ja': '終了'},
    };

    String getDialogText(String key) {
      final textMap = dialogTexts[key];
      if (textMap == null) return key;
      return textMap[currentLang] ?? textMap['zh']!;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: Text(getDialogText('title')),
            content: Text('${getDialogText('content')}$error'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _startInitialization(); // 重试
                },
                child: Text(getDialogText('retry')),
              ),
              TextButton(
                onPressed: () {
                  SystemNavigator.pop(); // 退出应用
                },
                child: Text(getDialogText('exit')),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor:
          isDark ? theme.scaffoldBackgroundColor : const Color(0xFFF9FAFB),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors:
                isDark
                    ? [
                      theme.colorScheme.surface,
                      theme.colorScheme.surface.withValues(alpha: 0.8),
                    ]
                    : [const Color(0xFFF9FAFB), const Color(0xFFFFFFFF)],
          ),
        ),
        child: SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Spacer(flex: 2),

              // Logo动画
              AnimatedBuilder(
                animation: _logoAnimationController,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _logoScaleAnimation.value,
                    child: Opacity(
                      opacity: _logoOpacityAnimation.value,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(24),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(24),
                          child: Image.asset(
                            'assets/images/app_icon.png',
                            width: 120,
                            height: 120,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: 32),

              // 加载指示器
              SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2.5,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    theme.colorScheme.primary,
                  ),
                ),
              ),

              const Spacer(),

              // 加载进度区域
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 48),
                child: Column(
                  children: [
                    // 进度条
                    AnimatedBuilder(
                      animation: _progressAnimation,
                      builder: (context, child) {
                        return LinearProgressIndicator(
                          value: _progressAnimation.value,
                          backgroundColor:
                              theme.colorScheme.surfaceContainerHighest,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            theme.colorScheme.primary,
                          ),
                          minHeight: 4,
                        );
                      },
                    ),

                    const SizedBox(height: 16),

                    // 状态文本
                    Text(
                      _currentStatus,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(
                          alpha: 0.8,
                        ),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const Spacer(),
            ],
          ),
        ),
      ),
    );
  }
}
